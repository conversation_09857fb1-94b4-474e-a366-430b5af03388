// 活跃用户表
{
	"bsonType": "object",
	"description": "给周月维度的用户基础统计和留存统计提供数据，每日跑批合并，仅添加本周/本月首次访问的用户。",
	"required": [],
	"permission": {
		"read": false,
		"create": false,
		"update": false,
		"delete": false
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"appid": {
			"bsonType": "string",
			"description": "应用ID"
		},
		"platform_id": {
			"bsonType": "string",
			"description": "应用平台ID，对应uni-stat-app-platforms._id",
			"foreignKey": "uni-stat-app-platforms._id"
		},
		"channel_id": {
			"bsonType": "string",
			"description": "渠道\/场景值ID，对应uni-stat-app-channels._id",
			"foreignKey": "uni-stat-app-channels._id"
		},
		"version_id": {
			"bsonType": "string",
			"description": "应用版本ID，对应opendb-app-versions._id",
			"foreignKey": "opendb-app-versions._id"
		},
		"uid": {
			"bsonType": "string",
			"description": "用户编号, 对应uni-id-users._id"
		},
		"dimension": {
			"bsonType": "string",
			"description": "时间范围 week:周，month:月",
			"enum": [{
				"text": "月",
				"value": "month"
			}, {
				"text": "周",
				"value": "week"
			}]
		},
		"create_time": {
			"bsonType": "timestamp",
			"description": "创建时间"
		}
	}
}