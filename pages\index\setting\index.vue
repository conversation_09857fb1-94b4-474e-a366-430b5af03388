<template>
  <page-container ref="pageContainerRef" :title="$t('setting.settings')" :is-back="false" :has-tab-bar="true" :is-show-nav="false" bgColorPage="linear-gradient(to bottom, #E2ECEE, #fafafa)">
    <view class="setting-container">
			<view class="setting-logo flex flex-row justify-center mb-32">
				<image
					src="@/static/login/home-logo.png"
					mode="scaleToFill"
					class="w-300 h-172 mt-140 mx-auto"
				/>
			</view>
      <!-- 遍历设置分组 -->
      <view v-for="(group, groupIndex) in settingGroups" :key="groupIndex" class="setting-group">
        <!-- 分组标题 -->
        <view class="group-title">{{ group.title }}</view>

        <!-- 分组内容 -->
        <uv-cell-group :border="false" class="cell-group">
          <template v-for="(item, index) in group.items" :key="index">
            <uv-cell
              v-if="!item.customContent"
              :is-link="item.isLink"
              :customStyle="customStyle"
              :arrow="item.arrow"
              @click="item.onClick && item.onClick()"
              :clickable="true"
              :border="false"
              class="cell-item"
            >
              <template #icon v-if="item.icon">
								<image
									v-if="item.iconType === 'img'"
									:src="item.src"
									mode="scaleToFill"
									class="w-44 h-44"
								/>
                <uni-icons
                  v-else-if="item.iconType === 'uni'"
                  size="30"
                  :type="item.icon"
                  color="var(--text-color-secondary)"
                />
                <uv-icon v-else :name="item.icon" color="var(--text-color-secondary)" />
              </template>
              <template #title v-if="item.title">
                <text class="text-secondary text-32 font-600 pl-8">{{ item.title }}</text>
              </template>
              <template #title v-else-if="item.isUniLink">
                <uni-link
                  :href="item.href"
                  :text="item.linkText"
                  :showUnderLine="false"
                  color="var(--text-color-secondary)"
									class="font-600 pl-8"
                  fontSize="18"
                />
              </template>
              <template #right-icon>
                <component v-if="item.slotComponent"
                  :is="item.slotComponent.component"
                  v-bind="item.slotComponent.props || {}"
                  v-on="item.slotComponent.events || {}"
                />
								<uv-icon v-else name="play-right-fill" color="var(--text-color-secondary)" size="10"></uv-icon>
              </template>
              <template #value v-if="item.value">
                <text class="text-secondary text-28">{{ item.value }}</text>
              </template>
              <template #label v-if="item.label">
                <text class="text-secondary text-28">{{ item.label }}</text>
              </template>
            </uv-cell>
          </template>
        </uv-cell-group>
      </view>
    </view>
    <CustomDialog
      v-model="unlockModel"
      contentHeight="120rpx"
      :title="$t('setting.closeConfirm')"
      @close="unlockModel = false"
      @confirm="confirmCloseUnlock"
    >
      <view class="h-full flex items-center justify-center">
        <text class="text-secondary">{{ $t('setting.confirmCloseUnlock') }}</text>
      </view>
    </CustomDialog>
  </page-container>
</template>

<script setup>
  import PageContainer from '@/components/PageContainer/index.vue';
  import CustomDialog from '@/components/CustomDialog/index.vue';
  import { settingGroups, pageContainer, confirmCloseUnlock, unlockModel } from './useMethods';
  import { ref, onMounted } from 'vue';
  import { $t } from '@/locale/index.js';

  const customStyle = {
    backgroundColor: 'var(--bg-color)',
    color: 'var(--bg-color-secondary)',
  };

  const pageContainerRef = ref(null);

  onMounted(() => {
    // 将本地ref赋值给全局pageContainer
    if (pageContainerRef.value) {
      pageContainer.value = pageContainerRef.value;
    }
  });
</script>

<style lang="scss" scoped>
  .setting-container {
    padding: 10rpx 0;
  }

  .setting-group {
    margin-bottom: 10rpx;
  }

  .group-title {
    padding: 20rpx 30rpx;
    font-size: 28rpx;
    color: var(--text-color-tertiary);
    font-weight: 500;
  }

  .cell-group {
    overflow: hidden;
    margin: 0 20rpx;
		border-radius: 32rpx;
  }

  .cell-item {
    transition: all 0.2s ease;
		padding: 4rpx 0;
    &:active {
      background-color: #f9f9f9;
      transform: scale(0.98);
    }
  }
</style>
