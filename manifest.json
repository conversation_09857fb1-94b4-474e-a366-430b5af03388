{
	"name": "宝塔面板",
	"appid": "__UNI__1EA994C",
	"description": "堡塔，让运维简单高效",
	"versionName": "5.1.3",
	"versionCode": 513,
	"transformPx": false,
	/* 5+App特有相关 */
	"app-plus": {
		"optimization": {
			"subPackages": true
		},
		"runmode": "liberate",
		"usingComponents": true,
		"nvueStyleCompiler": "uni-app",
		"compilerVersion": 3,
		"splashscreen": {
			"alwaysShowBeforeRender": true,
			"waiting": true,
			"autoclose": true,
			"delay": 0
		},
		/* 模块配置 */
		"modules": {
			"Barcode": {},
			"Camera": {}
		},
		/* 应用发布信息 */
		"distribute": {
			/* android打包配置 */
			"android": {
				"permissions": [
					"<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
					"<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
					"<uses-permission android:name=\"android.permission.VIBRATE\"/>",
					"<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
					"<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
					"<uses-permission android:name=\"android.permission.CAMERA\"/>",
					"<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
					"<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
					"<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
					"<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
					"<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
					"<uses-feature android:name=\"android.hardware.camera\"/>",
					"<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
				],
				"abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"],
				"minSdkVersion": 24,
				"targetSdkVersion": 36
			},
			/* ios打包配置 */
			"ios": {
				"dSYMs": false,
				"privacyDescription": {
					"NSPhotoLibraryUsageDescription": "允许访问照片以从图库选择二维码来绑定面板",
					"NSPhotoLibraryAddUsageDescription": "使用摄像头扫描二维码来绑定面板",
					"NSCameraUsageDescription": "使用摄像头扫描二维码来绑定面板",
					"NSLocalNetworkUsageDescription": "请放心，开启权限不会获取您所在网络上的其它设备信息，该权限仅用于提升浏览体验"
				}
			},
			/* SDK配置 */
			"sdkConfigs": {},
			"icons": {
				"android": {
					"hdpi": "unpackage/res/icons/72x72.png",
					"xhdpi": "unpackage/res/icons/96x96.png",
					"xxhdpi": "unpackage/res/icons/144x144.png",
					"xxxhdpi": "unpackage/res/icons/192x192.png"
				},
				"ios": {
					"appstore": "unpackage/res/icons/1024x1024.png",
					"ipad": {
						"app": "unpackage/res/icons/76x76.png",
						"app@2x": "unpackage/res/icons/152x152.png",
						"notification": "unpackage/res/icons/20x20.png",
						"notification@2x": "unpackage/res/icons/40x40.png",
						"proapp@2x": "unpackage/res/icons/167x167.png",
						"settings": "unpackage/res/icons/29x29.png",
						"settings@2x": "unpackage/res/icons/58x58.png",
						"spotlight": "unpackage/res/icons/40x40.png",
						"spotlight@2x": "unpackage/res/icons/80x80.png"
					},
					"iphone": {
						"app@2x": "unpackage/res/icons/120x120.png",
						"app@3x": "unpackage/res/icons/180x180.png",
						"notification@2x": "unpackage/res/icons/40x40.png",
						"notification@3x": "unpackage/res/icons/60x60.png",
						"settings@2x": "unpackage/res/icons/58x58.png",
						"settings@3x": "unpackage/res/icons/87x87.png",
						"spotlight@2x": "unpackage/res/icons/80x80.png",
						"spotlight@3x": "unpackage/res/icons/120x120.png"
					}
				}
			},
			"splashscreen": {
				"useOriginalMsgbox": true,
				"androidStyle": "default",
				"android": {
					"hdpi": "static/login/start-image.png",
					"xhdpi": "static/login/start-image.png",
					"xxhdpi": "static/login/start-image.png"
				},
				"iosStyle": "storyboard",
				"ios": {
					"storyboard": "C:/Users/<USER>/Desktop/宝塔/宝塔app资料/CustomStoryboard.zip"
				}
			}
		},
		"nativePlugins": {
			"Mpaas-Scan": {
				"AppId": "ALIPUBC28561D151013",
				"WorkspaceId": "default",
				"License": "eN18EgAei1ZrzJG6bUpsvpRgIG5J1qWbpL8BuGsle+/LU/lN0GeTNJeN8Tyg5YgzVmR5zrd6dzt8rYpXmM5/NgnNeD3hbMrWqaJqUfUF6mPoEdUj/8ABJC/q1wCTkRc69boI1Q8JpK0wU4hZTAO8ZEWFo9gxd/JzqCoim6YiA4yiYTl8h2GDhZEDXlRcqZRMOZSfAw9H/kK6baIm7S67HzEgtVJeoKiEf980Cl2zz74Iqv1inTFzY2v72k3voC8qqdfmIy519Rt/4H7RBQhOqIi7vPczufpJd2yJOddNqxzZXUijRbN4pCVCpQ17PAogEO3djmi1Xj5FBOJglOJ6Eg==",
				"__plugin_info__": {
					"name": "支付宝原生扫码插件",
					"description": "支付宝原生扫码组件，包体积仅0.7MB，15分钟即可完成接入。同时，mPaaS提供「扫码分析」大盘，",
					"platforms": "Android,iOS",
					"url": "",
					"android_package_name": "",
					"ios_bundle_id": "",
					"isCloud": false,
					"bought": -1,
					"pid": "",
					"parameters": {
						"AppId": {
							"des": "Android平台的AppId，请填写Android的config文件中的appId对应的值",
							"key": "mobilegw.appid",
							"value": ""
						},
						"WorkspaceId": {
							"des": "Android平台的WorkspaceId，请填写Android的config文件中的workspaceId对应的值",
							"key": "workspaceId",
							"value": ""
						},
						"License": {
							"des": "Android平台的License,，请填写Android的config文件中的mpaasConfigLicense对应的值",
							"key": "mpaasConfigLicense",
							"value": ""
						}
					}
				}
			}
		},
		"uniStatistics": {
			"enable": true
		}
	},
	/* 快应用特有相关 */
	"quickapp": {},
	/* 小程序特有相关 */
	"mp-weixin": {
		"appid": "",
		"setting": {
			"urlCheck": false
		},
		"usingComponents": true,
		"uniStatistics": {
			"enable": true
		}
	},
	"mp-alipay": {
		"usingComponents": true,
		"uniStatistics": {
			"enable": true
		}
	},
	"mp-baidu": {
		"usingComponents": true,
		"uniStatistics": {
			"enable": true
		}
	},
	"mp-toutiao": {
		"usingComponents": true,
		"uniStatistics": {
			"enable": true
		}
	},
	"uniStatistics": {
		"enable": true,
		"version": "2"
	},
	"vueVersion": "3",
	"app-harmony": {
		"distribute": {
			"bundleName": "cn.bt.btpanel.huawei",
			"splashScreens": {
				"startWindowIcon": "static/login/start-image.png"
			},
			"icons": {
				"foreground": "static/login/logo-start.png",
				"background": "static/login/logo-end.png"
			},
			"signingConfigs": {
				"default": {
					"certpath": "c:\\Users\\<USER>\\AppData\\Roaming\\HBuilder X\\extensions\\launcher\\agc-certs\\1751514266232.cer",
					"keyAlias": "debugKey",
					"keyPassword": "0000001B3344DD89330B478BE65CD09AB4EA7DD8A084BCAFC96D3EA4338998AC60E3C26D7F9234CFFAC57A",
					"profile": "c:\\Users\\<USER>\\AppData\\Roaming\\HBuilder X\\extensions\\launcher\\agc-certs\\1751514266232.p7b",
					"signAlg": "SHA256withECDSA",
					"storeFile": "c:\\Users\\<USER>\\AppData\\Roaming\\HBuilder X\\extensions\\launcher\\agc-certs\\1751514266232.p12",
					"storePassword": "0000001B3344DD89330B478BE65CD09AB4EA7DD8A084BCAFC96D3EA4338998AC60E3C26D7F9234CFFAC57A"
				}
			}
		},
		"uniStatistics": {
			"enable": true
		}
	},
	"h5": {
		"uniStatistics": {
			"enable": true
		}
	},
	"mp-harmony": {
		"uniStatistics": {
			"enable": true
		}
	},
	"mp-jd": {
		"uniStatistics": {
			"enable": true
		}
	},
	"mp-kuaishou": {
		"uniStatistics": {
			"enable": true
		}
	},
	"mp-lark": {
		"uniStatistics": {
			"enable": true
		}
	},
	"mp-qq": {
		"uniStatistics": {
			"enable": true
		}
	},
	"mp-xhs": {
		"uniStatistics": {
			"enable": true
		}
	},
	"quickapp-webview-huawei": {
		"uniStatistics": {
			"enable": true
		}
	},
	"quickapp-webview-union": {
		"uniStatistics": {
			"enable": true
		}
	},
	"locale": "zh-Hans",
	"fallbackLocale": "en"
}
