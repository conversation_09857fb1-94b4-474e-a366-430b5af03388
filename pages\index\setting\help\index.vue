<template>
	<page-container title="帮助中心" bgColorPage="#fafafa">
		<view class="content">
			<uv-cell-group :border="false">
				<uv-cell :border="false" title="绑定服务器教程" size="36">
					<template #right-icon>
						<uv-icon name="play-right-fill" color="var(--text-color-secondary)" size="10"></uv-icon>
					</template>
				</uv-cell>
				<uv-cell :border="false" title="添加动态口令教程" size="36">
					<template #right-icon>
						<uv-icon name="play-right-fill" color="var(--text-color-secondary)" size="10"></uv-icon>
					</template>
				</uv-cell>
			</uv-cell-group>
		</view>
		<view class="novice-container">
			
		</view>
	</page-container>
</template>

<script setup>
	import PageContainer from '@/components/PageContainer/index.vue';
	import { onLoad } from '@dcloudio/uni-app';
	import { ref } from 'vue';

	const type = ref('');
	const tap = ref('not'); // mixin

	// 使用import.meta.url方式引入图片
	const images = [
		'/static/novice/novice-one.png',
		'/static/novice/novice-two.png',
		'/static/novice/novice-three.png',
		'/static/novice/novice-four.png',
		'/static/novice/novice-five.png',
	];

	const messageImages = ['/static/novice/selected.png', '/static/novice/tips.png'];
	const checkImages = ['/static/novice/notive1.png', '/static/novice/notive2.png'];

	onLoad((options) => {
		type.value = options.type;
	});

	const clickUse = () => {
		uni.navigateBack({
			delta: 1,
		});
	};

	const clickGreen = () => {
		uni.$utils.showToast('对，绿色文字点了都是有效果的', 2000);
	};

	const checkImg = (index) => {
		uni.previewImage({
			urls: checkImages,
			current: index,
		});
	};

	const messageImg = (index) => {
		uni.previewImage({
			urls: messageImages,
			current: index,
		});
	};

	const clickPreview = (index) => {
		uni.previewImage({
			urls: images,
			current: index,
		});
	};
</script>

<style>
	.content {
		padding: 40rpx;
	}

	.qianyan view {
		font-size: 30rpx;
		padding-top: 15rpx;
		color: var(--text-color-secondary);
	}

	.img-one {
		margin-bottom: 20rpx;
	}

	.title {
		font-size: 30rpx;
		line-height: 52rpx;
		color: var(--text-color-primary);
		font-weight: 600;
		margin-top: 40rpx;
		margin-bottom: 20rpx;
	}

	.query {
		font-size: 28rpx;
		color: var(--text-color-secondary);
		line-height: 48rpx;
		margin-bottom: 20rpx;
		padding-left: 10rpx;
	}

	.use-app {
		width: 80%;
		background-color: #20a53a;
		height: 80rpx;
		border-radius: 40rpx;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #fff;
		margin: 10% auto;
	}
</style>
