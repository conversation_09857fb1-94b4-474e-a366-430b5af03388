<template>
  <theme-provider v-slot="{ theme }">
    <view class="page-container" :class="`theme-${theme}`" :style="{ background: bgColorPage }">
      <!-- 自定义导航栏 -->
      <custom-nav :title="title" :is-back="isBack" :bg-color="bgColor" v-show="isShowNav">
        <!-- 左侧区域插槽 -->
        <template #left>
          <slot name="nav-left"></slot>
        </template>
        <!-- 中间标题 -->
        <template #nav-title>
          <slot name="title"></slot>
        </template>
        <!-- 右侧区域插槽 -->
        <template #right>
          <slot name="nav-right"></slot>
        </template>
      </custom-nav>
      <bt-tabs v-if="showTabBar" :tabData="tabData" v-model:value="activeIndex" @tabClick="onTabClick" />
      <!-- 页面内容区域 -->
      <scroll-view
        ref="scrollView"
        class="content-area"
        :class="{ 'with-nav': isShowNav, 'with-tabbar': hasTabBar, 'with-tabs': showTabBar }"
        scroll-y
        :show-scrollbar="false"
        :scroll-top="scrollTop"
        @scroll="onScroll"
      >
        <slot></slot>
      </scroll-view>
      <uv-notify ref="notify" safeAreaInsetTop></uv-notify>
    </view>
  </theme-provider>
</template>

<script setup>
  import { defineProps, ref, watch, nextTick } from 'vue';
  import ThemeProvider from '@/components/ThemeProvider/index.vue';
  import CustomNav from '@/components/customNav/index.vue';
  import BtTabs from '@/components/BtTabs/index.vue';
  // 定义组件名称
  defineOptions({
    name: 'PageContainer',
  });

  // 定义属性
  const props = defineProps({
    // 页面标题
    title: {
      type: String,
      default: '页面标题',
    },
    // 是否显示返回按钮
    isBack: {
      type: Boolean,
      default: true,
    },
    // 导航栏背景色，为空则使用主题默认颜色
    bgColor: {
      type: String,
      default: '',
    },
    // 是否显示导航栏
    isShowNav: {
      type: Boolean,
      default: true,
    },
    // 是否考虑底部 tabbar 的高度
    hasTabBar: {
      type: Boolean,
      default: false,
    },
    // 是否显示标签栏
    showTabBar: {
      type: Boolean,
      default: false,
    },
    // 标签栏数据
    tabData: {
      type: Array,
      default: [],
    },
    // 背景色
    bgColorPage: {
      type: String,
      default: 'var(--bg-color)',
    },
  });

  const emits = defineEmits(['tabClick', 'scroll']);
  const activeIndex = defineModel('activeIndex', {
    type: Number,
    default: 0,
  });

  const paging = ref(null);
  const scrollView = ref(null);
  const notify = ref(null);
  const scrollTop = ref(0);

  const onTabClick = (e) => {
    emits('tabClick', e);
  };

  const onScroll = (e) => {
    emits('scroll', e);
  };

  // 重置滚动位置到顶部
  const resetScrollPosition = () => {
    scrollTop.value = 1; // 先设置为1
    nextTick(() => {
      scrollTop.value = 0; // 再设置为0，触发滚动重置
    });
  };

  defineExpose({
    notify,
    resetScrollPosition,
  });
</script>

<style lang="scss">
  .page-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    .content-area {
      width: 100%;
      box-sizing: border-box;
      height: 100vh;

      &.with-nav {
        height: calc(100vh - var(--status-bar-height) - 88rpx);
      }

      &.with-tabbar {
        height: calc(100vh - 174rpx);
      }

      &.with-tabs {
        height: calc(100vh - 174rpx - 90rpx);
      }

      &.with-nav.with-tabbar {
        height: calc(100vh - var(--status-bar-height) - 88rpx - 174rpx);
      }
    }
  }
</style>
