<template>
	<view class="ssh-terminal">
        <!-- 终端容器 -->
        <view class="term-container" id="appTerm" :prop="termConfig" :change:prop="term.initTerm"></view>
        <!-- 底部按钮栏 -->
		<view class="bottom-btn row" :style="{'visibility': keyboard ? 'visible' : 'hidden'}">
			<scroll-view scroll-x="true" class="scroll-symbol row" show-scrollbar="true">
				<view class="scroll-box row">
					<view v-for="symbol in specialSymbols" :key="symbol" :data-line="symbol" @click="term.addSymbol">{{ symbol }}</view>
				</view>
			</scroll-view>
			<view class="hide-key" style="width: 14%;" @touchend.prevent="pasteCommand" :prop="pasteInfo" :change:prop="term.changePaste">粘贴</view>
			<view class="hide-key" style="width: 16%" @click="hideKeyboard">收起键盘</view>
		</view>

        
	</view>
</template>

<script>
	import CustomDialog from '@/components/CustomDialog/index.vue';
    import { PATH, UUID, SECRET, AESKEY, SYSTEM } from '../../utils/config'
    import { checkField } from '../../utils'
    import { checkDebugMode as checkDebugModeApi, getSSHConnectionInfo } from '@/api/ssh';

    export default {
        name: 'SSHTerminal',
	    components: {
	    	CustomDialog
	    },
        data() {
            return {
                termConfig: {},
                keyboard: false, // 软键盘是否正在弹出状态
                specialSymbols: ['.', '-', '/', '~', '|', '_', '&', '`', '#', "'", '$', '"', '^'],
                pasteInfo: {
					data: '', // 要粘贴的值
					date: '', // 每次得到的值都不一样，用于触发数据驱动prop
				},
                showDeveloperDialog: false, // 开发者模式提醒弹窗
				showLoginDialog: false, // 登录弹窗
				loginTitle: '服务器验证', // 登录弹窗标题
				loginIP: '127.0.0.1', // 登录IP
				loginPort: '', // 登录端口
				loginUser: '', // 登录用户
				loginPwd: '', // 登录密码
				loginKey: '', // 登录私钥
				checkType: 'pwd', // 验证方式
				isLogin: 0, // 是否登录
            }
        },
        created() {
	    	// 监听键盘高度变化
	    	uni.onKeyboardHeightChange((key) => {
	    		this.keyboard = key.height > 0;
	    	});
	    },
		onLoad() {
			const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';
            // 将配置传递给 renderjs
            this.termConfig = {
                path: PATH.value,
                uuid: UUID.value,
                aeskey: AESKEY.value,
                secret: SECRET.value,
				url: DEFAULT_API_TYPE == 'domestic' ? '/webssh' : '/v2/webssh'
            }
			const initPath = PATH.value
			const titlePath = initPath.substring(initPath.lastIndexOf('/') + 1, initPath.lastIndexOf(':'))

			if (!checkField(titlePath)) {
				this.$nextTick(() => {
					uni.setNavigationBarTitle({
						title: titlePath
					})
					uni.setNavigationBarColor({
						backgroundColor: '#000000',
						frontColor: '#ffffff'
					})
				})
			}
        },
        onShow() {
            this.checkDebugMode()
        },
        methods: {
            // 检查开发者模式
		    checkDebugMode() {
		    	checkDebugModeApi().then(res => {
		    		if (res.status && res.data === 'True') {
		    			this.showDeveloperDialog = true;
		    		} else {
                        this.showDeveloperDialog = false;
                    }
		    	}).catch(err => {
		    		console.error('检查开发者模式失败', err);
		    	});
		    },
            // 开发者模式 - 点击退出
		    cancelDeveloper() {
		    	uni.navigateBack();
		    },
            // 开发者模式 - 前往面板管理
		    affirmDeveloper() {
		    	uni.navigateTo({
					url: '/pages/panel/panel'
				});
		    },
			// 点击验证方式
			clickCheckType(type) {
				this.checkType = type;
			},
            // 粘贴命令
		    pasteCommand() {
		    	uni.getClipboardData({
		    		success: (res) => {
		    			this.pasteInfo.data = res.data;
		    			this.pasteInfo.date = Date.parse(new Date());
		    		},
		    		fail: () => {
		    			uni.showToast({
		    				title: '获取剪贴板内容失败',
		    				icon: 'none',
		    				duration: 2000
		    			});
		    		}
		    	});
		    },
            // 点击收起键盘
		    hideKeyboard() {
		    	uni.hideKeyboard();
		    },
            // 显示登录弹窗
            showLoginPopup(res) {
				const data = res.info

				if (data.indexOf("@127.0.0.1") != -1) {
					this.loginUser = data.split('@')[0].split(',')[1]
					this.loginPort = data.split('1:')[1]
				}
				
                this.showLoginDialog = true;
            },
			// 点击登录
			clickLogin(close) {
				uni.showLoading({
					title: "处理中...",
					mask: true
				});

				const paramJson = {
					host: this.loginIP,
					port: this.loginPort,
					username: this.loginUser,
					password: this.loginPwd,
					ps: '服务器连接'
				};

				if (this.checkType === 'key') {
					paramJson.pkey = this.loginKey;
					paramJson.password = '';
				}
				console.log(paramJson, 'paramJson')
				getSSHConnectionInfo(paramJson).then(res => {
					console.log(res, 'res')
					uni.hideLoading();
					if (res.status) {
						close();
						this.isLogin = Date.parse(new Date());
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 4000
						});
					}
					this.resetLoginInfo();
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: '连接失败，请检查网络',
						icon: 'none',
						duration: 4000
					});
				});
			},
			// 重置登录信息
			resetLoginInfo() {
				this.loginIP = '127.0.0.1';
				this.loginPort = '';
				this.loginUser = '';
				this.loginPwd = '';
				this.loginKey = '';
				this.checkType = 'pwd';
			}
        }
    }
</script>

<script module="term" lang="renderjs">
	import {
		Terminal
	} from 'xterm'


	import {
		FitAddon
	} from 'xterm-addon-fit'

	import md5 from 'js-md5'

    import { encrypt } from '../../utils/config'

    var term;
	var ws;
	var fitAddon;

    module.exports = {
        data() {
			return {
				rows: '',
				cols: '',
				config: {},
				clientHeight: 0,
				parseInfo: {
					data: '',
					date: ''
				}
			}
		},
        mounted() {

			

		},
        methods: {
			// 当isLogin的值改变时,说明连接成功了,触发此事件。 监听 service 层数据变更
			changeServer(newValue, oldValue, ownerInstance, instance) {
				this.socketConnection()
			},
            initTerm(newValue) {
                var _self = this

			    _self.config = newValue

			    const termElement = document.querySelector("#appTerm")
			    const fontSize = 13 // 与Terminal配置中的fontSize保持一致
			    const lineHeight = 1.2 // 与Terminal配置中的lineHeight保持一致
			    const charHeight = Math.floor(fontSize * lineHeight)
			    const charWidth = Math.floor(fontSize * 0.6) // 假设每个字符的宽度约为字体大小的0.6倍

			    _self.rows = Math.floor(termElement.clientHeight / charHeight)
			    _self.cols = Math.floor(termElement.clientWidth / charWidth)
                
			    // 光标样式,block,underline,bar
			    term = new Terminal({
			    	rendererType: "dom", // 渲染类型
			    	rows: _self.rows,
			    	cols: _self.cols,
			    	cursorBlink: true, // 光标是否闪烁
			    	cursorStyle: 'block', // 光标样式
			    	cursorWidth: 18, // 光标大小
			    	fontSize: 13, // 字体大小
			    	fontFamily: "Monaco, Menlo, Consolas, 'Courier New', monospace",
			    	letterSpacing: -3, // 字之间的间距
			    	lineHeight: 1.2, // 行高
			    })

			    // 全屏
			    fitAddon = new FitAddon()

			    term.loadAddon(fitAddon)
			    term.open(document.getElementById("appTerm"))
			    fitAddon.fit()

			    term.resize(_self.cols, _self.rows)

			    // 终端输入事件
			    term.onData(function(data) {
			    	ws.send(data)
			    })

			    // websocket连接
			    _self.socketConnection()

			    // 监听窗口resize事件
			    window.addEventListener("resize", this.resizeScreen)
            },
            // websocket连接
			socketConnection() {
                var timestamp = Date.parse(new Date()),
                    request_token = md5(String(timestamp) + md5(this.config.secret));

                var path = String(this.config.path).indexOf("https") == -1 ? 'ws://' : 'wss://'
                path += this.config.path.substring(this.config.path.lastIndexOf('/') + 1, this.config.path.length) + this.config.url

                var data = {}

                data['request_time'] = timestamp
                data['request_token'] = request_token

                var wsURL = path + "?client_bind_token=" + this.config.uuid + "&form_data=" + encodeURIComponent(encrypt(JSON
                    .stringify(data), this.config.aeskey))

                ws = new WebSocket(wsURL)

                term.clear()

                ws.addEventListener("open", this.socketOpen)
                ws.addEventListener("message", this.socketMessage)
                ws.addEventListener("close", this.socketClose)
                ws.addEventListener("error", this.socketError)
            },
            // 刚开始连接成功要发送的数据
			socketOpen(event) {

				var hostJson = {
					"host": '127.0.0.1',
				}
				ws.send(JSON.stringify(hostJson))

				var _self = this
				ws.send(JSON.stringify({
					resize: 1,
					rows: _self.rows,
					cols: _self.cols
				}))

				_self.clientHeight = document.querySelector("#appTerm").clientHeight

			},
            // 接收到websocket返回的数据并打印
			socketMessage(res) {

				var data = res.data
				term.write(data)
				if (data.indexOf("Authentication failed") != -1) {
					this.$ownerInstance.callMethod('showLoginPopup', {
						info: data
					})
					return
				}

			},
            socketClose() {
				console.log("close")
			},

			socketError() {
				console.log("error")
			},
            // 窗口大小发生改变时
			resizeScreen() {

                if (ws.readyState == 0 || this.clientHeight == 0) {
                    return
                }

                var nowClientHeight = document.querySelector("#appTerm").clientHeight

                var _self = this

                // 软键盘弹出
                if (this.clientHeight > nowClientHeight) {
                
                    fitAddon.fit()
                
                    term.resize(_self.cols, 18)
                
                    ws.send(JSON.stringify({
                        resize: 1,
                        rows: 18,
                        cols: _self.cols
                    }))
                
                    term.scrollToBottom()
                
                } else { // 软键盘收起
                
                    fitAddon.fit()
                
                    term.resize(_self.cols, _self.rows)
                
                    ws.send(JSON.stringify({
                        resize: 1,
                        rows: _self.rows,
                        cols: _self.cols
                    }))
                
                    term.scrollToBottom()
                
                }

            },
            // 点击符号
			addSymbol(e) {

                term.focus()

                var line = e.currentTarget.dataset.line

                ws.send(line)

            },
            // 点击了粘贴，改变了普通script的值，然后触发了数据驱动prop，数据更新就会调用此方法
			changePaste(newValue, oldValue, ownerInstance, instance) {
                
                term.focus()
                
                ws.send(newValue.data)
                
            },
        }
    }
</script>

<style>
@import 'xterm/css/xterm.css';

.ssh-terminal {
	position: relative;
	width: 100%;
}

.term-container {
	height: 103vh;
	background-color: black;
	padding: 0;
	margin: 0;
}

.xterm {
	padding: 0;
	margin: 0;
}

.xterm-viewport {
	overflow-y: auto;
	overflow-x: hidden;
	scrollbar-width: thin;
	scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.xterm-viewport::-webkit-scrollbar {
	width: 5px;
}

.xterm-viewport::-webkit-scrollbar-track {
	background: transparent;
}

.xterm-viewport::-webkit-scrollbar-thumb {
	background-color: rgba(255, 255, 255, 0.2);
	border-radius: 3px;
}

.xterm .xterm-screen {
	position: relative;
}

.xterm .xterm-rows {
	position: absolute;
	left: 0;
	top: 0;
	color: #fff;
	font-family: Monaco, Menlo, Consolas, 'Courier New', monospace;
	font-size: 13px;
	line-height: 1.2;
	padding-left: 5px;
}

.bottom-btn {
	width: 100%;
	height: 90rpx;
	color: #333;
	font-size: 32rpx;
	position: fixed;
	bottom: 0;
	z-index: 100;
	background-color: #f3f8f8;
}

.scroll-symbol {
	width: 70%;
	overflow: auto;
	height: 90rpx;
}

.scroll-box {
	width: 100%;
	height: 90rpx;
	display: flex;
	flex-direction: row;
}

.scroll-box>view {
	min-width: 16%;
	height: 90rpx;
	line-height: 90rpx;
	text-align: center;
}

.hide-key {
	font-size: 26rpx;
	height: 90rpx;
	line-height: 90rpx;
	text-align: center;
}

.xterm-rows {
	padding-left: 5px !important;
}

/* 服务器验证样式 */
.form-content {
	width: 100%;
}

.form-line {
	height: 1rpx;
	background-color: #eee;
	margin: 5rpx 0;
}

.form-line:nth-child(1) {
	margin-top: 0;
}

.form-item {
	padding: 15rpx 0;
	align-items: center;
	display: flex;
	flex-direction: row;
}

.form-name {
	width: 180rpx;
	font-size: 28rpx;
	color: #333;
}

.form-input-box {
	flex: 1;
}

.form-input-box input, .form-input-box textarea {
	height: 70rpx;
	font-size: 28rpx;
	color: #333;
}

.check-box {
	align-items: center;
	justify-content: flex-end;
	display: flex;
	flex-direction: row;
}

.check-text {
	width: 145rpx;
	height: 64rpx;
	line-height: 64rpx;
	color: #555;
	background-color: #fff;
	border: 1rpx solid #ccc;
	text-align: center;
	border-radius: 6rpx;
	font-size: 26rpx;
	margin-left: 10rpx;
}

.checked-text {
	color: #fff !important;
	background-color: #20a53a !important;
	border-color: #20a53a !important;
}

.placeholder {
	color: #999;
}

.private-key {
	width: 100%;
	height: 150rpx !important;
	overflow: auto;
}

.row {
  display: flex;
  flex-direction: row;
}

</style> 