<template>
  <page-container ref="pageContainer" :title="$t('home.fileManagement')" :is-show-nav="false" :has-tab-bar="true" bgColorPage="linear-gradient(to bottom, #E2ECEE, #fafafa)">
		<custom-nav :is-remove-status-bar="true" :is-back="false" bg-color="transparent" class="pt-40">
			<template #left>
				<image
					src="@/static/login/home-logo.png"
					mode="scaleToFill"
					class="w-150 h-80 mt-40 mr-20"
				/>
			</template>
			<template #right>
				<view class="mt-40 mx-15" @click="startScan">
					<uv-icon name="plus-circle" color="var(--text-color-tertiary)" size="32"></uv-icon>
				</view>
			</template>
			<template #nav-title>
				<uv-input
					v-model="searchText"
					placeholder="请输入服务器或者IP地址"
					prefixIcon="search"
					prefixIconStyle="font-size: 22px;color: #909399"
					shape="circle"
					border="surround"
					:customStyle="{ backgroundColor: '#fff', height: '60rpx' }"
					class="mt-40"
				></uv-input>
			</template>
		</custom-nav>
		<view v-if="!serverList.length" class="empty-container flex flex-col justify-center items-center h-50vh">
			<image
				src="@/static/login/home-logo.png"
				mode="scaleToFill"
				class="w-300 h-160 mt-40 mb-40 mx-auto"
			/>
			<text class="tips-text text-tertiary mb-20 text-28">尚未添加服务器</text>
		</view>
		<z-paging-empty-view
			v-else-if="!filteredServerList.length"
			:empty-view-text="'未找到相关服务器'"
			:show-empty-view="true"
		/>
    <ServerSelector
      v-else
      :server-list="filteredServerList"
      @select="onSelect"
      @delete="onDelete"
    />
  </page-container>
</template>

<script setup>
	import { ref, computed } from 'vue';
	import CustomNav from '@/components/customNav/index.vue';
  import PageContainer from '@/components/PageContainer/index.vue';
  import ServerSelector from '@/components/ServerSelector/index.vue';
  import EmptyServerList from '@/pages/index/serverList/emptyServerList.vue';
  import { useServerListStore } from '@/pages/index/serverList/store';
  import { $t } from '@/locale/index.js';
	import { getServerList, navigateServerInfo, onActionDelete } from '@/pages/index/serverList/useController';
	
	const { serverList } = useServerListStore().getReactiveState();
	const searchText = ref('');
  const pageContainer = ref(null);
  const startScan = () => {
    uni.$emit('scan');
  };

  const onDelete = async (item) => {
    await onActionDelete(item);
    pageContainer.value.notify.success($t('common.deleteSuccess'));
  };

  const onSelect = (item) => {
    navigateServerInfo(item, () => {
      uni.navigateTo({
        url: `/linux/files/index`,
        animationType: 'zoom-fade-out',
      });
    });
	};
	
	const filteredServerList = computed(() => {
		if (!searchText.value) return serverList.value;
		const keyword = searchText.value.trim().toLowerCase();
		return serverList.value.filter(item =>
			(item.name && item.name.toLowerCase().includes(keyword)) ||
			(item.ip && item.ip.toLowerCase().includes(keyword))
		);
	});
</script>

<style scoped>
.no-search-result {
  min-height: 200rpx;
  color: #999;
  font-size: 28rpx;
}
</style>